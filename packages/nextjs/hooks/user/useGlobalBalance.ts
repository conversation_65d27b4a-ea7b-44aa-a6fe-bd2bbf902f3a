import { useCallback, useEffect, useRef } from "react";
import { useTargetNetwork } from "@/hooks/scaffold-eth/useTargetNetwork";
import { useBalanceStore } from "@/services/store/store";
import { isAddress } from "ethers";
import { useBlockNumber } from "wagmi";

/**
 * 使用全局余额状态的 Hook
 * 基于专门的余额 store，确保所有组件间余额数据同步
 * 包含 backoff 重试机制、区块监听和定时刷新
 */
export function useGlobalBalance(
  proxyWallet: string | null,
  options?: {
    enableBlockWatch?: boolean;
    enableAutoRefresh?: boolean;
    refreshInterval?: number;
    skipInitialFetch?: boolean; // 跳过初始获取，避免重复请求
  },
) {
  const {
    enableBlockWatch = false, // 默认关闭区块监听，避免过度刷新
    enableAutoRefresh = true,
    refreshInterval = 30000,
    skipInitialFetch = false,
  } = options || {};

  const { refreshBalance: refreshBalanceAction, forceRefreshBalance, getBalanceState } = useBalanceStore();
  const { targetNetwork } = useTargetNetwork();
  const { data: blockNumber } = useBlockNumber({
    watch: enableBlockWatch,
    chainId: targetNetwork.id,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);
  const lastFetchTimeRef = useRef<number>(0); // 防抖：记录上次获取时间

  // 获取当前地址的余额状态
  const balanceState = getBalanceState(proxyWallet || "");

  // 手动刷新余额（带 backoff 重试）
  const refreshBalance = useCallback(
    async (force = false) => {
      if (!proxyWallet || !isAddress(proxyWallet)) {
        console.warn("⚠️ Invalid proxy wallet address for balance refresh");
        return;
      }

      if (force) {
        // 使用强制刷新，忽略缓存和智能判断
        await forceRefreshBalance(proxyWallet);
      } else {
        // 使用普通刷新
        await refreshBalanceAction(proxyWallet);
      }
    },
    [proxyWallet, refreshBalanceAction, forceRefreshBalance],
  );

  // 初始化和自动获取余额
  useEffect(() => {
    if (!proxyWallet || !isAddress(proxyWallet)) {
      return;
    }

    // 获取当前状态，但不作为依赖项
    const currentBalanceState = getBalanceState(proxyWallet);

    // 如果没有余额数据或数据过期，自动获取
    const now = Date.now();
    const isDataStale =
      !currentBalanceState || !currentBalanceState.balance || now - currentBalanceState.lastUpdated > refreshInterval;

    // 智能初始化策略：
    // 1. 如果是主组件（enableBlockWatch=true），总是获取
    // 2. 如果是从属组件但没有数据，也要获取（确保有数据显示）
    // 3. 如果明确跳过初始获取且有数据，则跳过
    const shouldFetch =
      !currentBalanceState?.isLoading &&
      isDataStale &&
      (enableBlockWatch || // 主组件总是获取
        !skipInitialFetch || // 没有明确跳过
        !currentBalanceState); // 没有任何数据时必须获取

    if (shouldFetch) {
      // 防抖：避免短时间内重复请求
      const now = Date.now();
      if (now - lastFetchTimeRef.current < 1000) {
        // 1秒内不重复请求
        return;
      }

      lastFetchTimeRef.current = now;
      refreshBalanceAction(proxyWallet);
    }
  }, [proxyWallet, refreshBalanceAction, refreshInterval, skipInitialFetch, enableBlockWatch, getBalanceState]);

  // 智能区块监听：只在特定条件下刷新
  useEffect(() => {
    if (!enableBlockWatch || !proxyWallet || !isAddress(proxyWallet) || !blockNumber) {
      return;
    }

    // 获取当前状态，但不作为依赖项
    const currentBalanceState = getBalanceState(proxyWallet);

    // 避免过度刷新：只在数据较旧时才刷新
    const now = Date.now();
    const timeSinceLastUpdate = currentBalanceState ? now - currentBalanceState.lastUpdated : Infinity;

    // 只有在以下情况才刷新：
    // 1. 没有余额数据
    // 2. 数据超过15秒旧（减少限制，确保及时刷新）
    // 3. 上次更新失败
    if (
      !currentBalanceState ||
      timeSinceLastUpdate > 15000 ||
      (currentBalanceState.error && timeSinceLastUpdate > 3000)
    ) {
      refreshBalanceAction(proxyWallet);
    }
  }, [blockNumber, proxyWallet, refreshBalanceAction, enableBlockWatch, getBalanceState]);

  // 定时刷新
  useEffect(() => {
    if (!enableAutoRefresh || !proxyWallet || !isAddress(proxyWallet)) {
      return;
    }

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      if (isMountedRef.current) {
        refreshBalanceAction(proxyWallet);
      }
    }, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [proxyWallet, refreshBalanceAction, enableAutoRefresh, refreshInterval]);

  // 组件卸载时清理
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    balance: balanceState?.balance || null,
    isLoading: balanceState?.isLoading || false,
    error: balanceState?.error || null,
    refreshBalance,
    lastUpdated: balanceState?.lastUpdated || 0,
  };
}
