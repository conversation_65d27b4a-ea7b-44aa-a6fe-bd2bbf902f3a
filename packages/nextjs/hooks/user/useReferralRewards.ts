"use client";

import { useCallback, useEffect, useState } from "react";
import { getInvitedUsersByCode, getMysteryboxByProxyWallet } from "@/api/user";
import computeProxyAddress from "@/contracts/computeProxyAddress";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { signRewardAuth } from "@/utils/signature/signMysteryBoxAuth";
import { useTranslation } from "react-i18next";

interface MysteryBox {
  id: string;
  owner: string;
  proxy_wallet: string;
  timestamp_created: string;
  reward: number;
  timestamp_latest_opened: string | null;
  timestamp_next_ready: string;
}

interface ReferralRewardState {
  isNewUser: boolean;
  isInvitedUser: boolean;
  inviterCode: string | null;
  totalInvites: number;
  hasUnOpenedBox: boolean;
  mysteryBoxes: MysteryBox[];
  loading: boolean;
  error: string | null;
  lastRewardCheck: string | null; // 添加最后检查时间，防止重复检查
}
const REWARD_API_BASE = (process.env.NEXT_PUBLIC_MYSTERY_BOX_API || "http://localhost:5201").replace(
  /\/api(\/mystery-box)?$/,
  "",
);

export const useReferralRewards = () => {
  const { t } = useTranslation();
  const { address, isConnected } = useUserAddress();
  const { magic: magicInstance } = useMagicStore();
  const [proxyWallet, setProxyWallet] = useState<string>("");
  const [state, setState] = useState<ReferralRewardState>({
    isNewUser: false,
    isInvitedUser: false,
    inviterCode: null,
    totalInvites: 0,
    hasUnOpenedBox: false,
    mysteryBoxes: [],
    loading: false,
    error: null,
    lastRewardCheck: null,
  });

  // 计算代理钱包地址
  const initializeProxyWallet = useCallback(async () => {
    if (!address) return;

    try {
      const proxyAddress = await computeProxyAddress(address);
      setProxyWallet(proxyAddress);
    } catch (error) {
      console.error("Failed to compute proxy address:", error);
      setState(prev => ({ ...prev, error: "Failed to compute proxy address" }));
    }
  }, [address]);

  const checkUserStatusAndRewards = useCallback(
    async (forceCheck = false) => {
      if (!proxyWallet || !address) return;

      // 防止短时间内重复检查（除非强制检查）
      const now = Date.now().toString();
      if (!forceCheck && state.lastRewardCheck && Date.now() - parseInt(state.lastRewardCheck) < 5000) {
        console.log("跳过重复检查，距离上次检查不足5秒");
        return;
      }

      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        // 1. 获取或创建用户邀请码
        const invitationResponse = await fetch("/api/invitation/get-or-create", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ proxy_wallet: proxyWallet }),
        });

        const invitationData = await invitationResponse.json();
        const currentUser = invitationData.success ? invitationData.data : null;

        // 2. 获取用户的盲盒信息
        const mysteryBoxResponse = await getMysteryboxByProxyWallet(proxyWallet);
        const mysteryBoxes: MysteryBox[] = mysteryBoxResponse?.data?.mysterybox || [];

        // 3. 检查是否有未开启的盒子
        const hasUnOpenedBox = mysteryBoxes.some(box => !box.timestamp_latest_opened);

        // 4. 调用后端检查并创建奖励（现在需要签名验证）
        let rewardCheckResult = null;
        try {
          // 生成签名（仅Magic用户）
          const signatureData = await signRewardAuth(address, magicInstance);

          const rewardResponse = await fetch(`${REWARD_API_BASE}/api/reward/check-and-create`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(signatureData),
          });

          if (rewardResponse.ok) {
            const rewardData = await rewardResponse.json();
            rewardCheckResult = rewardData.success ? rewardData.data : null;
            console.log("奖励检查结果:", rewardCheckResult);
          }
        } catch (error) {
          console.warn("Failed to check rewards:", error);
        }

        // 5. 获取用户信息
        const isInvitedUser = currentUser ? !!currentUser.invited_by : false;
        const inviterCode = currentUser ? currentUser.invited_by || null : null;

        // 6. 获取当前用户邀请的人数
        let totalInvites = 0;
        if (currentUser && currentUser.invitation_code) {
          try {
            const invitedResponse = await getInvitedUsersByCode(currentUser.invitation_code);
            totalInvites = invitedResponse?.data?.web_users?.length || 0;
          } catch (error) {
            console.warn("Failed to get invited users:", error);
          }
        }

        // 7. 如果后端创建了新奖励，重新获取盲盒信息
        let finalMysteryBoxes = mysteryBoxes;
        let finalHasUnOpenedBox = hasUnOpenedBox;

        if (rewardCheckResult && rewardCheckResult.createdBoxes > 0) {
          try {
            // 等待一下让数据库更新
            await new Promise(resolve => setTimeout(resolve, 1000));
            const updatedMysteryBoxResponse = await getMysteryboxByProxyWallet(proxyWallet);
            finalMysteryBoxes = updatedMysteryBoxResponse?.data?.mysterybox || mysteryBoxes;
            finalHasUnOpenedBox = finalMysteryBoxes.some(box => !box.timestamp_latest_opened);
          } catch (error) {
            console.warn("Failed to refresh mystery boxes:", error);
          }
        }

        setState(prev => ({
          ...prev,
          isNewUser: mysteryBoxes.length === 0, // 基于原始盒子数量判断是否新用户
          isInvitedUser,
          inviterCode,
          totalInvites,
          hasUnOpenedBox: finalHasUnOpenedBox,
          mysteryBoxes: finalMysteryBoxes,
          loading: false,
          lastRewardCheck: now,
        }));
      } catch (error) {
        console.error("Error checking user status:", error);
        setState(prev => ({
          ...prev,
          error: "Failed to check user status",
          loading: false,
        }));
      }
    },
    [proxyWallet, address, state.lastRewardCheck, magicInstance],
  );

  // 获取邀请奖励说明文本
  const getRewardDescription = useCallback(() => {
    const { isInvitedUser, totalInvites, hasUnOpenedBox, mysteryBoxes } = state;

    if (!hasUnOpenedBox && mysteryBoxes.length === 0) {
      return t("mysteryBox.referral.rewards.noBoxes");
    }

    const descriptions = [];

    if (hasUnOpenedBox) {
      const unOpenedCount = mysteryBoxes.filter(box => !box.timestamp_latest_opened).length;
      descriptions.push(t("mysteryBox.referral.rewards.unOpenedBoxes", { count: unOpenedCount }));
    }

    const openedCount = mysteryBoxes.filter(box => box.timestamp_latest_opened).length;
    if (openedCount > 0) {
      descriptions.push(t("mysteryBox.referral.rewards.openedBoxes", { count: openedCount }));
    }

    if (isInvitedUser) {
      descriptions.push(t("mysteryBox.referral.rewards.invitedUser"));
    }

    if (totalInvites > 0) {
      descriptions.push(t("mysteryBox.referral.rewards.invitedFriends", { count: totalInvites }));
    }

    return (
      descriptions.join(t("mysteryBox.referral.rewards.separator")) || t("mysteryBox.referral.rewards.systemActivated")
    );
  }, [state, t]);

  // 初始化
  useEffect(() => {
    if (isConnected && address) {
      initializeProxyWallet();
    }
  }, [isConnected, address, initializeProxyWallet]);

  return {
    ...state,
    proxyWallet,
    getRewardDescription,
    refreshStatus: checkUserStatusAndRewards,
  };
};
