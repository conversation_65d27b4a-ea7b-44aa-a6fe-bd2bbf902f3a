import { useEffect, useState } from "react";

interface UseClaimCountdownReturn {
  canClaim: boolean;
  countdown: string;
  nextClaimTime: Date | null;
}

/**
 * 自定义 Hook 用于处理手续费领取的28天倒计时逻辑
 * @param latestClaimTime 最后一次领取时间
 * @param claimIntervalDays 领取间隔天数，默认28天
 * @returns 返回是否可以领取、倒计时文本和下次可领取时间
 */
export const useClaimCountdown = (latestClaimTime: string | null, claimIntervalDays = 28): UseClaimCountdownReturn => {
  const [canClaim, setCanClaim] = useState<boolean>(true);
  const [countdown, setCountdown] = useState<string>("");
  const [nextClaimTime, setNextClaimTime] = useState<Date | null>(null);

  useEffect(() => {
    // 计算下次可领取时间
    const calculateNextClaimTime = (): Date | null => {
      if (!latestClaimTime) return null;
      const lastClaimDate = new Date(latestClaimTime);
      const nextClaimDate = new Date(lastClaimDate.getTime() + claimIntervalDays * 24 * 60 * 60 * 1000);
      return nextClaimDate;
    };

    // 格式化倒计时文本
    const formatCountdown = (timeDiff: number): string => {
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      if (days > 0) {
        return `${days}天 ${hours}小时 ${minutes}分钟`;
      } else if (hours > 0) {
        return `${hours}小时 ${minutes}分钟 ${seconds}秒`;
      } else {
        return `${minutes}分钟 ${seconds}秒`;
      }
    };

    // 更新倒计时状态
    const updateCountdown = () => {
      const nextTime = calculateNextClaimTime();
      setNextClaimTime(nextTime);

      if (!nextTime) {
        setCanClaim(true);
        setCountdown("");
        return;
      }

      const now = new Date();
      const timeDiff = nextTime.getTime() - now.getTime();

      if (timeDiff <= 0) {
        setCanClaim(true);
        setCountdown("");
      } else {
        setCanClaim(false);
        setCountdown(formatCountdown(timeDiff));
      }
    };

    // 立即更新一次
    updateCountdown();

    // 设置定时器，每秒更新
    const interval = setInterval(updateCountdown, 1000);

    // 清理定时器
    return () => clearInterval(interval);
  }, [latestClaimTime, claimIntervalDays]);

  return {
    canClaim,
    countdown,
    nextClaimTime,
  };
};
