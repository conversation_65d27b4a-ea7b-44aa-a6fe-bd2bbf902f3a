import { useCallback } from "react";
import { useUserAddress } from "@/hooks/useUserAddress";
import { useMagicStore } from "@/services/store/magicStore";
import { WalletType, useGlobalState } from "@/services/store/store";
import { clearStorage } from "@/utils";
import { signOut } from "next-auth/react";
import { useDisconnect } from "wagmi";

export const useLoginActions = () => {
  const { disconnect } = useDisconnect();
  const { isMagic } = useUserAddress();
  const { logout: logoutMagic } = useMagicStore();
  const { setWalletType } = useGlobalState();

  const logout = useCallback(async () => {
    try {
      await signOut({
        redirect: false,
        callbackUrl: "/",
      });

      if (isMagic && logoutMagic) {
        await logoutMagic();
      } else {
        disconnect();
      }

      setWalletType(WalletType.NONE);
      clearStorage();

      // 清除所有 cookies
      const domains = ["predict.one", "localhost"];
      domains.forEach(domain => {
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${domain}`;
        });
      });

      console.log("User logged out successfully");
    } catch (error) {
      console.error("Logout failed:", error);
      throw error;
    }
  }, [isMagic, logoutMagic, disconnect, setWalletType]);

  return {
    logout,
  };
};
