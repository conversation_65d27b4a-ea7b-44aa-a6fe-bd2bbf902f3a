import { useEffect, useRef } from "react";

const useWebSocketBooksList = (
  url: string,
  assetsIds: string[],
  setBtnDataList: (btnDataList: any[] | ((prevBtnDataList: any[]) => any[])) => void,
) => {
  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    const flattenedAssetsIds = assetsIds.flat();

    // 如果没有asset IDs，不创建WebSocket连接
    if (!flattenedAssetsIds || flattenedAssetsIds.length === 0) {
      return;
    }

    const socket = new WebSocket(url);
    socketRef.current = socket;

    const handleOpen = () => {
      const subscribeMessage = {
        operation: "subscribe",
        event: "book",
        assetIds: flattenedAssetsIds,
      };
      socket.send(JSON.stringify(subscribeMessage));
    };

    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        if (data.operation === "book" && data.event) {
          const eventData = JSON.parse(data.event);

          setBtnDataList((prevBtnDataList: any[]) => {
            const updatedBtnDataList = prevBtnDataList.map(btnData => {
              if (btnData.yesBookData?.asset_id === eventData.asset_id) {
                return {
                  ...btnData,
                  yesBookData: {
                    ...btnData.yesBookData,
                    ...eventData,
                  },
                };
              } else if (btnData.noBookData?.asset_id === eventData.asset_id) {
                return {
                  ...btnData,
                  noBookData: {
                    ...btnData.noBookData,
                    ...eventData,
                  },
                };
              }
              return btnData;
            });

            return [...updatedBtnDataList];
          });
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    const handleError = (error: Event) => {
      console.error("WebSocket error:", error);
    };

    socket.addEventListener("open", handleOpen);
    socket.addEventListener("message", handleMessage);
    socket.addEventListener("error", handleError);

    return () => {
      if (socket) {
        if (socket.readyState === WebSocket.OPEN) {
          const unsubscribeMessage = {
            operation: "unsubscribe",
            event: "book",
          };
          socket.send(JSON.stringify(unsubscribeMessage));
        }

        socket.removeEventListener("open", handleOpen);
        socket.removeEventListener("message", handleMessage);
        socket.removeEventListener("error", handleError);
        socket.close();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url, JSON.stringify(assetsIds)]);
};

const useWebSocketLastTradePrice = (
  url: string,
  assetsIds: string[],
  setPriceDataList: (priceDataList: { asset_id: string; price: number; side: string }[]) => void,
) => {
  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    const socket = new WebSocket(url);
    socketRef.current = socket;
    const flattenedAssetsIds = assetsIds.flat();

    const handleOpen = () => {
      const subscribeMessage = {
        operation: "subscribe",
        event: "last_trade_price",
        assetIds: flattenedAssetsIds,
      };
      socket.send(JSON.stringify(subscribeMessage));
    };

    const handleMessage = (event: MessageEvent) => {
      const data = JSON.parse(event.data);

      if (data.operation === "last_trade_price" && data.event) {
        const eventData = JSON.parse(data.event);

        const { asset_id, price, side } = eventData;

        setPriceDataList([{ asset_id, price: parseFloat(price), side }]);
      }
    };

    const handleError = (error: Event) => {
      console.error("WebSocket error:", error);
    };

    socket.addEventListener("open", handleOpen);
    socket.addEventListener("message", handleMessage);
    socket.addEventListener("error", handleError);

    return () => {
      if (socket.readyState === WebSocket.OPEN) {
        const unsubscribeMessage = {
          operation: "unsubscribe",
          event: "last_trade_price",
        };
        socket.send(JSON.stringify(unsubscribeMessage));
      }

      socket.removeEventListener("open", handleOpen);
      socket.removeEventListener("message", handleMessage);
      socket.removeEventListener("error", handleError);
      socket.close();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url, assetsIds]);
};

export { useWebSocketBooksList, useWebSocketLastTradePrice };
